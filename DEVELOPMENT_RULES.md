# Eldrath Development Rules & Guidelines 🗡️

## Core Development Philosophy
**"Build it simple, build it working, then build it awesome."**

### The Golden Rules 🏆

1. **ONE SYSTEM AT A TIME** - No jumping between systems until current one is complete and tested
2. **PLAYABLE FIRST** - Every milestone should result in something you can actually play
3. **NO FEATURE CREEP** - Stick to the current task. Write down new ideas for later
4. **TEST EVERYTHING** - If it doesn't work in-game, it doesn't count as done
5. **KEEP IT SIMPLE** - Start with the simplest version that works, optimize later

### Development Phases 📋

#### Phase 1: Foundation (CURRENT)
- [ ] Basic Godot project setup
- [ ] Simple player character that can move around
- [ ] Basic world with collision
- [ ] Core architecture for future systems
- **GOAL**: Character walks around a simple map

#### Phase 2: Core Mechanics
- [ ] Basic combat (attack, dodge, stamina)
- [ ] Simple enemy AI
- [ ] Health/stamina systems
- **GOAL**: You can fight and defeat enemies

#### Phase 3: World Building
- [ ] Multiple connected areas
- [ ] Basic NPCs and dialogue
- [ ] Simple quest system
- **GOAL**: Small but complete game loop

#### Phase 4: Advanced Systems
- [ ] Morality system
- [ ] Magic and corruption
- [ ] Equipment system
- **GOAL**: Core RPG mechanics working

#### Phase 5: Content & Polish
- [ ] Procedural dungeons
- [ ] Full story implementation
- [ ] Audio and visual polish
- **GOAL**: Complete game experience

### Feature Creep Prevention 🚫

#### The "Parking Lot" System
When you get excited about a new feature or improvement:
1. **STOP** what you're doing
2. **WRITE IT DOWN** in the "Future Ideas" section below
3. **GET BACK** to the current task
4. **REVIEW** the parking lot only when current phase is complete

#### Current Phase Rules
- **NO** adding new features until current milestone is 100% complete
- **NO** "quick improvements" that aren't part of current task
- **NO** "while I'm here" additions
- **YES** to bug fixes that break current functionality

### Technical Guidelines 🛠️

#### Code Quality Standards
- **Keep it readable** - Code should tell a story
- **One responsibility per class** - Don't make god objects
- **Comment the why, not the what** - Explain decisions, not syntax
- **Test in-game frequently** - Don't code for hours without testing

#### Godot Best Practices
- Use scenes for everything reusable
- Signals for communication between objects
- Groups for managing collections of similar objects
- Autoloads only for truly global systems

#### File Organization
```
res://
├── scenes/
│   ├── player/
│   ├── enemies/
│   ├── world/
│   └── ui/
├── scripts/
│   ├── player/
│   ├── systems/
│   └── managers/
├── assets/
│   ├── sprites/
│   ├── audio/
│   └── fonts/
└── data/
    ├── items/
    ├── enemies/
    └── quests/
```

### Milestone Completion Criteria ✅

A milestone is only complete when:
1. **Core functionality works** in-game
2. **No game-breaking bugs** exist
3. **Code is clean** and commented
4. **Next milestone can begin** without refactoring current work

### Emergency Brake Procedures 🚨

If you find yourself:
- Working on the same problem for more than 2 hours
- Adding features not in current milestone
- Refactoring working code "just because"
- Feeling overwhelmed by complexity

**STOP** and:
1. Save your work
2. Step back and reassess
3. Ask for help or guidance
4. Consider simplifying the approach

### Future Ideas Parking Lot 🅿️
*Write new ideas here instead of implementing them immediately*

#### Gameplay Ideas
- [ ] Weather system affecting gameplay
- [ ] Day/night cycle with different enemies
- [ ] Crafting system for weapons/potions
- [ ] Mount/companion system

#### Technical Improvements
- [ ] Save/load system
- [ ] Settings menu
- [ ] Controller support
- [ ] Performance optimization

#### Content Ideas
- [ ] Additional regions beyond the 5 planned
- [ ] Mini-games (fishing, gambling, etc.)
- [ ] Housing/base building
- [ ] Multiplayer co-op

---

## Current Focus 🎯
**Phase 1: Foundation**
**Current Task**: Project Setup & Foundation
**Next Milestone**: Character moving around a basic world

Remember: **Simple, working, and playable beats complex, broken, and theoretical every time!**
